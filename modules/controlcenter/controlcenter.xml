<esh name="ControlCenter">
    <BoxV h_expand="true" name="control-center-widgets">
        <BoxH name="top-widget" h_expand="true">
            <BoxV name="wb-widget" style_classes="menu" spacing="5">
                <Button name="wifi-widget" on_clicked="self.open_wifi">
                    <BoxH>
                        <Svg path="config/assets/icons/wifi.svg" style_classes="icon" name="wifi-svg"/>
                        <BoxV name="wifi-widget-info">
                            <Label label="Wi-Fi" style_classes="title ct" h_align="start"/>
                            <Ref>self.wlan_label</Ref>
                        </BoxV>
                    </BoxH>
                </Button>
                <Button name="bluetooth-widget" on_clicked="self.open_bluetooth">
                    <BoxH>
                        <Svg path="config/assets/icons/bluetooth.svg" style_classes="icon" name="bluetooth-svg"/>
                        <BoxV name="bluetooth-widget-info">
                            <Label label="Bluetooth" style_classes="title ct" h_align="start"/>
                            <Ref>self.bluetooth_label</Ref>
                        </BoxV>
                    </BoxH>
                </Button>
            </BoxV>
            <BoxH name="dnd-widget" style_classes="menu" h_expand="true">
                <Button name="focus-widget" on_clicked="self.set_dont_disturb">
                    <BoxH>
                        <Svg path="config/assets/icons/dnd-off.svg" style_classes="icon" name="focus-icon"/>
                        <BoxV name="focus-widget-info">
                            <Label label="Do Not Disturb" style_classes="title ct" h_align="start"/>
                            <Label label="Off" style_classes="subtitle" h_align="start"/>
                        </BoxV>
                    </BoxH>
                </Button>
            </BoxH>
        </BoxH>
        <BoxV name="brightness-widget" style_classes="menu" h_expand="true">
            <Label label="Display" style_classes="title" h_align="start"/>
            <Ref>self.brightness_scale</Ref>
            <Svg path="config/assets/icons/brightness.svg" style_classes="icon" name="brightness-icon"/>
        </BoxV>
        <BoxV name="volume-widget" style_classes="menu" h_expand="true">
            <Label label="Sound" style_classes="title" h_align="start"/>
            <Ref>self.volume_scale</Ref>
            <Svg path="config/assets/icons/volume.svg" style_classes="icon" name="volume-icon"/>
        </BoxV>
        <BoxV>
            <Ref>self.music_widget</Ref>
        </BoxV>
    </BoxV>
</esh>
